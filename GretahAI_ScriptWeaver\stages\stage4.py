"""
Stage 4: UI Element Detection and Test Case Step Selection

This module handles UI element detection, interactive element selection,
and element matching for test case steps.
Maintains the StateManager pattern and follows the established architectural patterns.
"""

import os
import logging
import streamlit as st
import time
from datetime import datetime

# Configure logging
logger = logging.getLogger("ScriptWeaver.stage4")

# Import helper functions from other modules
from core.element_detection import detect_elements, detect_elements_advanced, filter_qa_relevant_elements, select_element_interactively
from core.element_matching import match_elements_with_ai
from helpers_pure import analyze_step_for_test_data
from state_manager import StateStage


def stage4_ui_detection_and_matching(state):
    """Phase 4: UI Element Detection and Test Case Step Selection."""
    st.markdown("<h2 class='stage-header'>Phase 4: UI Element Detection</h2>", unsafe_allow_html=True)

    # Check if we have a stage progression message to display
    if 'stage_progression_message' in st.session_state:
        st.success(st.session_state['stage_progression_message'])
        # Remove the message so it doesn't show up again
        del st.session_state['stage_progression_message']

    # Check if we're coming from Stage 7 (automatic advancement)
    if 'coming_from_stage7' in st.session_state:
        # Get information about the advancement
        if 'force_refresh_after_advance' in st.session_state:
            from_step = st.session_state['force_refresh_after_advance'].get('from_step')
            target_step = st.session_state['force_refresh_after_advance'].get('target_step')

            # Display a compact banner
            st.success(f"✅ Advanced from Step {from_step} to Step {target_step}")

        # Clear the flag so it doesn't show up again
        del st.session_state['coming_from_stage7']

    # Check if we need to force a refresh after automatic advancement
    if 'force_refresh_after_advance' in st.session_state:
        # Get the timestamp to see if this is a recent advancement
        advance_time = st.session_state['force_refresh_after_advance'].get('timestamp')
        from datetime import datetime, timedelta
        now = datetime.now()

        # If the advancement was within the last 5 seconds, force a refresh
        if advance_time and (now - datetime.strptime(advance_time, "%H:%M:%S.%f")) < timedelta(seconds=5):
            # Clear the flag so we don't keep refreshing
            target_step = st.session_state['force_refresh_after_advance'].get('target_step')
            logger.info(f"Forcing refresh to ensure UI shows step {target_step}")
            del st.session_state['force_refresh_after_advance']

            # Force a rerun to refresh the UI
            time.sleep(0.5)
            st.rerun()

    # Check if prerequisites are met
    if not hasattr(state, 'step_table_json') or not state.step_table_json:
        st.warning("⚠️ Please complete Phase 3 first to convert the test case")
        return

    # Step selection section in a collapsible section
    with st.expander("Step Selection", expanded=True):
        # Add a reset button to start over with step 1
        reset_cols = st.columns([3, 1])
        with reset_cols[0]:
            if st.button("Reset to Step 1", key="reset_step_btn", help="Reset to the first step of the test case"):
                # Check if we have progress to confirm reset
                has_progress = (hasattr(state, 'current_step_index') and state.current_step_index > 0) or state.all_steps_done

                if has_progress:
                    st.warning("⚠️ Resetting will clear all progress.")
                    confirm_reset = st.button("Confirm Reset", key="confirm_reset_step")

                    if not confirm_reset:
                        st.info("Reset cancelled.")
                        return

                # Use the state manager's update method to reset step progress
                state.update_step_progress(
                    current_step_index=0,
                    all_steps_done=False,
                    step_ready_for_script=False,
                    script_just_generated=False
                )

                # Reset step-specific state
                state.reset_step_state(confirm=True, reason="User requested reset to Step 1")

                st.success("✅ Reset complete.")
                st.rerun()

    # Get total steps if not already set (use effective step count for hybrid editing)
    if state.total_steps == 0:
        if hasattr(state, 'get_effective_total_steps'):
            state.total_steps = state.get_effective_total_steps()
        elif state.step_table_json:
            state.total_steps = len(state.step_table_json)

    # Show completion message if all steps are processed
    if state.all_steps_done:
        st.success("✅ All test case steps have been processed!")
        return

    # Show "Proceed to next step" button if the current step is ready for script and there are more steps
    if state.step_ready_for_script:
        # Get the next step information
        next_step_index = state.current_step_index + 1
        if next_step_index < state.total_steps:
            next_step = state.step_table_json[next_step_index]
            next_step_no = next_step.get('step_no', 'N/A')
            next_step_action = next_step.get('action', 'N/A')

            # Add section for the next step button
            st.markdown("---")
            _show_next_step_banner(state, next_step_no, next_step_action)
            _create_proceed_button(state, next_step_no, next_step_action, "stage4")
            return

    # Get steps from the effective step table (includes hybrid editing)
    step_table_json = state.get_effective_step_table() if hasattr(state, 'get_effective_step_table') else state.step_table_json

    if not step_table_json or not isinstance(step_table_json, list):
        st.error("Step table JSON is not available or not in the correct format")
        return

    # Sync step table if hybrid editing is active
    if hasattr(state, 'sync_step_table_with_combined'):
        state.sync_step_table_with_combined()

    # Create step selection dropdown from the step table
    step_options = []
    for step in step_table_json:
        if isinstance(step, dict):
            step_no = step.get('step_no', 'N/A')
            action = step.get('action', 'N/A')
            # Add visual indicator for manual steps
            step_indicator = "✏️" if step.get('_is_manual', False) else "🤖"
            step_options.append(f"{step_indicator} Step {step_no} - {action}")

    if not step_options:
        st.warning("No steps found in the step table")
        return

    # Initialize to the first step if needed
    if state.current_step_index < 0 or state.current_step_index >= len(step_options):
        state.current_step_index = 0
        logger.info(f"Initializing current_step_index to 0 (was out of bounds)")

    # Log the current step index for debugging
    logger.info(f"Current step index: {state.current_step_index}, Total steps: {len(step_options)}")

    # Get the current step option
    try:
        # Check if we're coming from Stage 7 with a specific step
        if 'coming_from_stage7' in st.session_state and 'force_refresh_after_advance' in st.session_state:
            target_step = st.session_state['force_refresh_after_advance'].get('target_step')
            logger.info(f"Coming from Stage 7, looking for step {target_step}")

            # Find the step option that matches the target step
            matching_options = [opt for opt in step_options if f"Step {target_step}" in opt]
            if matching_options:
                # Find the index of the matching option
                target_index = step_options.index(matching_options[0])

                # If the current index doesn't match the target, update it
                if state.current_step_index != target_index:
                    logger.info(f"Updating current_step_index from {state.current_step_index} to {target_index} to match target step {target_step}")
                    state.current_step_index = target_index

        # Get the current step option based on the (possibly updated) index
        current_step_option = step_options[state.current_step_index]
        logger.info(f"Selected step option: {current_step_option}")
    except IndexError:
        # Handle index error gracefully
        logger.error(f"Index error: current_step_index={state.current_step_index}, len(step_options)={len(step_options)}")
        st.error(f"Error: Step index {state.current_step_index} is out of range (0-{len(step_options)-1})")
        # Reset to a valid index
        state.current_step_index = 0
        current_step_option = step_options[0]
        logger.info(f"Reset to step option: {current_step_option}")

    # Create step selection UI in a collapsible section
    with st.expander("Step Navigation", expanded=True):
        # Create columns for step selection and navigation
        step_col1, step_col2 = st.columns([3, 1])

        with step_col1:
            # Allow direct selection of steps with a selectbox
            selected_index = step_options.index(current_step_option)
            selected_step_option = st.selectbox(
                "Select Test Case Step",
                step_options,
                index=selected_index,
                help="Select a specific test case step to process"
            )

            # If user selected a different step, update the current step index
            if selected_step_option != current_step_option:
                new_index = step_options.index(selected_step_option)

                # Show a warning about changing steps
                st.warning("⚠️ Switching steps will reset current progress.")

                # Add a confirmation button
                if st.button("Confirm Step Change", key="confirm_step_change"):
                    # Check if the current step has any progress
                    has_step_progress = (
                        (hasattr(state, 'step_elements') and state.step_elements) or
                        (hasattr(state, 'step_matches') and state.step_matches) or
                        (hasattr(state, 'test_data') and state.test_data) or
                        (hasattr(state, 'test_data_skipped') and state.test_data_skipped) or
                        (hasattr(state, 'step_ready_for_script') and state.step_ready_for_script) or
                        (hasattr(state, 'script_just_generated') and state.script_just_generated)
                    )

                    # Log the step change with progress information
                    logger.info(f"Changing step from {state.current_step_index} to {new_index} (has_progress={has_step_progress})")

                    # Use the state manager's update method to update step index
                    state.update_step_progress(current_step_index=new_index)

                    # Reset step-specific state
                    state.reset_step_state(confirm=True, reason=f"User changed from step {state.current_step_index + 1} to step {new_index + 1}")

                    st.success(f"✅ Changed to Step {new_index + 1}")
                    st.rerun()

                # If not confirmed, revert to the current step
                selected_step_option = current_step_option

        with step_col2:
            # Add step completion tracking
            if hasattr(state, 'completed_steps') and isinstance(state.completed_steps, list):
                completed_count = len(state.completed_steps)
                st.metric("Steps Completed", f"{completed_count} of {state.total_steps}")
            else:
                # Initialize completed_steps if not present
                state.completed_steps = []
                st.metric("Steps Completed", f"0 of {state.total_steps}")

    # Set the selected step option for the rest of the code to use
    # This will be the current step unless the user confirmed a change

    if selected_step_option != "Select a step...":
        # Extract step number more robustly to handle hybrid editing formats
        step_no_raw = selected_step_option.split(" - ")[0]
        # Remove emoji indicators and "Step " prefix
        step_no = step_no_raw.replace("✏️", "").replace("🤖", "").replace("Step ", "").strip()
        logger.info(f"Processing step_no: {step_no} from selected_step_option: {selected_step_option}")

        # Find the selected step in the step table
        selected_step_table_entry = next(
            (step for step in step_table_json if str(step.get('step_no')) == step_no),
            None
        )

        if selected_step_table_entry:
            logger.info(f"Found step table entry for step {step_no}: {selected_step_table_entry.get('action')}")
        else:
            logger.error(f"Could not find step table entry for step {step_no}")
            # Log all available steps for debugging
            for step in step_table_json:
                logger.info(f"Available step: {step.get('step_no')} - {step.get('action')}")

        # Also find the corresponding original step
        original_steps = state.selected_test_case.get('Steps', [])
        selected_original_step = next(
            (step for step in original_steps if str(step.get('Step No')) == step_no),
            None
        )

        if selected_original_step:
            logger.info(f"Found original step for step {step_no}: {selected_original_step.get('Test Steps')}")
        else:
            logger.error(f"Could not find original step for step {step_no}")
            # Log all available original steps for debugging
            for step in original_steps:
                logger.info(f"Available original step: {step.get('Step No')} - {step.get('Test Steps')}")

        if selected_step_table_entry and selected_original_step:
            # Store both versions in state manager
            state.selected_step_table_entry = selected_step_table_entry
            state.selected_step = selected_original_step

            # Show context from previous steps if available
            _display_previous_step_context(state, selected_original_step)

            # Display step details in a collapsible section
            with st.expander("Step Details", expanded=True):
                # Create tabs for different views of the step
                step_tab1, step_tab2 = st.tabs(["Automation-Ready Format", "Original Format"])

                with step_tab1:
                    # Display the automation-ready step details
                    col1, col2 = st.columns(2)
                    with col1:
                        st.markdown(f"**Step Number:** {selected_step_table_entry.get('step_no')}")
                        st.markdown(f"**Action:** {selected_step_table_entry.get('action')}")
                        st.markdown(f"**Locator Strategy:** {selected_step_table_entry.get('locator_strategy')}")
                        st.markdown(f"**Locator:** {selected_step_table_entry.get('locator')}")
                    with col2:
                        st.markdown(f"**Test Data Parameter:** {selected_step_table_entry.get('test_data_param')}")
                        st.markdown(f"**Expected Result:** {selected_step_table_entry.get('expected_result')}")
                        st.markdown(f"**Assertion Type:** {selected_step_table_entry.get('assertion_type')}")
                        st.markdown(f"**Timeout:** {selected_step_table_entry.get('timeout')} seconds")

                with step_tab2:
                    # Display the original step details
                    st.markdown(f"**Step Number:** {selected_original_step.get('Step No')}")
                    st.markdown(f"**Test Steps:** {selected_original_step.get('Test Steps')}")
                    st.markdown(f"**Expected Result:** {selected_original_step.get('Expected Result')}")

            # Check if this step should trigger automatic stage progression
            progression_triggered = _check_automatic_stage_progression(state, selected_step_table_entry, selected_original_step)

            # If automatic progression was triggered, return early to avoid rendering UI elements
            if progression_triggered:
                logger.info(f"Automatic stage progression triggered for step {selected_original_step.get('Step No')} - stopping UI rendering")
                return

            # Stage 4b: UI Element Detection
            _handle_ui_element_detection(state, selected_step_table_entry, selected_original_step)

            # Stage 4c: Element Matching
            _handle_element_matching(state, selected_step_table_entry, selected_original_step)


def _check_automatic_stage_progression(state, selected_step_table_entry, selected_original_step):
    """
    Check if the selected step should trigger automatic stage progression.

    This function analyzes the step to determine if it can be processed automatically
    without user interaction, and if so, advances to the appropriate next stage.

    Args:
        state (StateManager): The application state manager instance
        selected_step_table_entry (dict): The automation-ready step table entry
        selected_original_step (dict): The original test case step
    """
    try:
        # Check if this is a navigation step that doesn't require UI elements
        locator_strategy = selected_step_table_entry.get('locator_strategy', '')
        is_navigation_step = locator_strategy in ["", "none", "n/a", "url"]

        # Check if we already have element matches for this step (from previous processing)
        test_case_id = state.selected_test_case.get('Test Case ID')
        step_no = str(selected_original_step.get('Step No'))
        has_existing_matches = (
            hasattr(state, 'element_matches') and
            state.element_matches and
            test_case_id in state.element_matches and
            step_no in state.element_matches[test_case_id]
        )

        # Check if we have manually selected elements for this step
        has_manual_selection = (
            has_existing_matches and
            any(match.get('manually_selected', False)
                for match in state.element_matches[test_case_id][step_no])
        )

        # Enhanced logging for debugging
        logger.info(f"Automatic progression check for step {step_no}:")
        logger.info(f"  - locator_strategy: '{locator_strategy}'")
        logger.info(f"  - is_navigation_step: {is_navigation_step}")
        logger.info(f"  - test_case_id: '{test_case_id}'")
        logger.info(f"  - has_existing_matches: {has_existing_matches}")
        logger.info(f"  - has_manual_selection: {has_manual_selection}")
        logger.info(f"  - current_stage: {state.current_stage}")

        # Debug element matches structure
        if hasattr(state, 'element_matches') and state.element_matches:
            logger.info(f"  - element_matches keys: {list(state.element_matches.keys())}")
            if test_case_id in state.element_matches:
                logger.info(f"  - steps in element_matches[{test_case_id}]: {list(state.element_matches[test_case_id].keys())}")
        else:
            logger.info(f"  - element_matches: None or empty")

        # If this is a navigation step or we already have matches, process automatically
        if is_navigation_step or has_existing_matches:
            logger.info(f"Step {step_no} qualifies for automatic progression - analyzing test data requirements")

            # Analyze if the step requires test data
            try:
                test_data_analysis = analyze_step_for_test_data(
                    selected_step_table_entry,
                    selected_original_step.get('Test Steps')
                )
                logger.info(f"Test data analysis for step {step_no}: requires_test_data={test_data_analysis.get('requires_test_data', 'unknown')}")
            except Exception as e:
                logger.error(f"Error in test data analysis for step {step_no}: {e}")
                # Fallback to requiring test data to be safe
                test_data_analysis = {
                    "requires_test_data": True,
                    "reason": f"Error in analysis: {e}",
                    "data_types": []
                }

            # Create or update element matches structure
            if not hasattr(state, 'element_matches'):
                state.element_matches = {}

            if test_case_id not in state.element_matches:
                state.element_matches[test_case_id] = {}

            # For navigation steps, create empty matches if they don't exist
            if is_navigation_step and step_no not in state.element_matches[test_case_id]:
                state.element_matches[test_case_id][step_no] = []
                logger.info(f"Created empty element matches for navigation step {step_no}")

            # Update state with analysis results
            state.step_matches = state.element_matches
            state.llm_step_analysis = {
                "requires_ui_element": not is_navigation_step,
                "reason": "Navigation step - no UI elements needed" if is_navigation_step else "UI elements already matched",
                "matches": state.element_matches[test_case_id].get(step_no, []),
                "requires_test_data": test_data_analysis["requires_test_data"],
                "test_data_reason": test_data_analysis["reason"],
                "data_types": test_data_analysis["data_types"]
            }

            # Store test data analysis separately for easier access
            state.test_data_analysis = test_data_analysis

            # Determine next stage based on test data requirements
            if not test_data_analysis["requires_test_data"]:
                # No test data needed - advance to Stage 6
                state.test_data_skipped = True
                state.test_data = getattr(state, 'test_data', {})  # Ensure test_data exists

                logger.info(f"Step {step_no} requires no test data - attempting to advance to Stage 6")
                logger.info(f"Current stage before advance: {state.current_stage}")

                if state.current_stage == StateStage.STAGE4_DETECT:
                    advance_success = state.advance_to(StateStage.STAGE6_GENERATE, f"Step {step_no} processed automatically, no test data needed - advancing to Stage 6")
                    logger.info(f"Stage advance to Stage 6 success: {advance_success}")
                    logger.info(f"Current stage after advance: {state.current_stage}")

                    if advance_success:
                        # Force state update in session state
                        st.session_state['state'] = state
                        st.session_state['stage_progression_message'] = f"✅ Step {step_no} processed automatically. Proceeding to Stage 6 (no test data needed)."

                        logger.info(f"Calling st.rerun() for stage transition to Stage 6")
                        # Call st.rerun() to immediately refresh the UI
                        st.rerun()
                        return True
                    else:
                        logger.error(f"Failed to advance to Stage 6 for step {step_no}")
                else:
                    logger.warning(f"Not in Stage 4 - current stage is {state.current_stage}, cannot advance to Stage 6")
            else:
                # Test data needed - advance to Stage 5
                logger.info(f"Step {step_no} requires test data - attempting to advance to Stage 5")
                logger.info(f"Current stage before advance: {state.current_stage}")

                if state.current_stage == StateStage.STAGE4_DETECT:
                    advance_success = state.advance_to(StateStage.STAGE5_DATA, f"Step {step_no} processed automatically - advancing to Stage 5 for test data")
                    logger.info(f"Stage advance to Stage 5 success: {advance_success}")
                    logger.info(f"Current stage after advance: {state.current_stage}")

                    if advance_success:
                        # Force state update in session state
                        st.session_state['state'] = state
                        st.session_state['stage_progression_message'] = f"✅ Step {step_no} processed automatically. Proceeding to Stage 5 for test data configuration."

                        logger.info(f"Calling st.rerun() for stage transition to Stage 5")
                        # Call st.rerun() to immediately refresh the UI
                        st.rerun()
                        return True
                    else:
                        logger.error(f"Failed to advance to Stage 5 for step {step_no}")
                else:
                    logger.warning(f"Not in Stage 4 - current stage is {state.current_stage}, cannot advance to Stage 5")

        # If we reach here, no automatic progression was triggered
        logger.info(f"No automatic progression triggered for step {step_no} - user interaction required")
        return False

    except Exception as e:
        logger.error(f"Error in automatic stage progression check: {e}")
        return False


def _show_next_step_banner(state, next_step_no, next_step_action):
    """
    Display a banner indicating the next step is ready.

    Shows progress information in the sidebar instead of the main content area.

    Args:
        state (StateManager): The application state manager instance
        next_step_no (str): The step number of the next step
        next_step_action (str): The action description of the next step
    """
    # Display progress information in the sidebar
    with st.sidebar:
        st.success(f"✅ Step {state.selected_step.get('Step No')} completed")
        st.info(f"Ready for Step {next_step_no}")

    # Add a small indicator in the main content area
    st.success("✅ Current step ready for next step")


def _create_proceed_button(state, next_step_no, next_step_action, stage_key):
    """
    Create a button to proceed to the next step.

    Creates a button that advances to the next step in the test case workflow
    when clicked, with simplified UI and clear manual progression.

    Args:
        state (StateManager): The application state manager instance
        next_step_no (str): The step number of the next step
        next_step_action (str): The action description of the next step
        stage_key (str): A unique identifier for the button based on the current stage
    """
    # Create a simple button with clear label
    button_label = f"Proceed to Step {next_step_no}"

    if st.button(button_label, key=f"proceed_to_next_step_{stage_key}", use_container_width=True):
        with st.spinner(f"Advancing to Step {next_step_no}..."):
            # Log the current state before advancing
            logger.info(f"Button clicked: Proceeding to Step {next_step_no}")

            # Store the current step index for comparison
            old_step_index = state.current_step_index

            # Reset the flags
            if state.step_ready_for_script:
                state.step_ready_for_script = False
                logger.info("State change: step_ready_for_script = False")

            # Store the current timestamp for debugging
            proceed_timestamp = datetime.now().strftime("%H:%M:%S.%f")

            # Get the current step number
            current_step_index = state.current_step_index
            next_step_index = current_step_index + 1

            # Check if the next step index is valid
            if next_step_index < state.total_steps:
                # Add the current step to the completed steps list
                if not hasattr(state, 'completed_steps'):
                    state.completed_steps = []

                # Get the current step number
                current_step_no = state.step_table_json[current_step_index].get('step_no')

                # Add to completed steps if not already there
                if current_step_no not in state.completed_steps:
                    state.completed_steps.append(str(current_step_no))

                # Update step progress
                state.update_step_progress(
                    current_step_index=next_step_index,
                    step_ready_for_script=False,
                    script_just_generated=False
                )

                # Reset step-specific state
                state.reset_step_state(confirm=True, reason=f"User proceeded from step {current_step_index + 1} to step {next_step_index + 1}")

                # Add a message to display on the next page
                st.session_state['stage_progression_message'] = f"✅ Proceeded to Step {next_step_no}"

                # Store information about the advancement for UI refresh
                st.session_state['force_refresh_after_advance'] = {
                    'timestamp': proceed_timestamp,
                    'from_step': current_step_no,
                    'target_step': next_step_no
                }

                # Rerun the app to show the next step
                st.rerun()
            else:
                # All steps are done
                state.all_steps_done = True
                st.success("✅ All test case steps have been processed!")
                st.rerun()


def _handle_ui_element_detection(state, selected_step_table_entry, selected_original_step):
    """Handle UI element detection for the selected test case step."""
    with st.expander("UI Element Detection", expanded=True):
        # Check if we have a step table analysis
        requires_ui_elements = True
        ui_element_reason = "UI element detection is needed for proper automation."

        if hasattr(state, 'step_table_analysis') and state.step_table_analysis:
            step_table_analysis = state.step_table_analysis
            requires_ui_elements = step_table_analysis.get("requires_ui_elements", True)
            ui_element_reason = step_table_analysis.get("reason", ui_element_reason)

        # Also check the specific step's requirements
        step_requires_ui_elements = selected_step_table_entry.get('locator_strategy') not in ["", "none", "n/a", "url"]

        # Display message about UI element detection requirement
        if requires_ui_elements and step_requires_ui_elements:
            st.info(f"🔍 {ui_element_reason}")

            # Show the Detect UI Elements and Interactive Selection buttons
            detect_col1, detect_col2 = st.columns(2)
            with detect_col1:
                detect_button = st.button(
                    "🔍 Detect Elements Automatically",
                    key="detect_ui_elements_btn",
                    help="Automatically detect UI elements from the website for this step",
                    use_container_width=True
                )

            with detect_col2:
                select_interactive_button = st.button(
                    "👆 Select Element Interactively",
                    key="select_element_interactive_btn",
                    help="Open a browser window to manually select UI elements for this step",
                    use_container_width=True
                )

            if select_interactive_button:
                _handle_interactive_element_selection(state, selected_step_table_entry, selected_original_step)

            if detect_button:
                _handle_automatic_element_detection(state, selected_step_table_entry)
        else:
            # If UI element detection is not needed, show a message and set empty elements
            st.success(f"✓ Element detection not needed: {ui_element_reason}")

            # Create empty elements for the workflow to continue
            if not hasattr(state, 'detected_elements') or not state.detected_elements:
                state.detected_elements = []
                state.qa_relevant_elements = []


def _handle_interactive_element_selection(state, selected_step_table_entry, selected_original_step):
    """Handle interactive element selection for the selected test case step."""
    with st.spinner("Opening browser for interactive element selection. Please select an element in the browser window..."):
        try:
            # Get locator strategy and value from the step table entry
            locator_strategy = None
            locator_value = None

            if selected_step_table_entry:
                locator_strategy = selected_step_table_entry.get('locator_strategy')
                locator_value = selected_step_table_entry.get('locator')

            # Launch interactive element selector
            website_url = state.website_url if hasattr(state, 'website_url') and state.website_url else "https://example.com"
            logger.info(f"Using website URL for interactive element selection: {website_url}")
            selected_element = select_element_interactively(website_url)

            if selected_element:
                # Convert the selected element to the format expected by the application
                element = {
                    'name': selected_element.get('tagName', '') + (f"#{selected_element.get('id')}" if selected_element.get('id') else ""),
                    'tag': selected_element.get('tagName', ''),
                    'selector': selected_element.get('cssSelector', ''),
                    'xpath': selected_element.get('xpath', ''),
                    'attributes': {
                        'id': selected_element.get('id', ''),
                        'name': selected_element.get('name', ''),
                        'class': selected_element.get('className', ''),
                        'type': selected_element.get('type', ''),
                        'value': selected_element.get('value', ''),
                        'placeholder': selected_element.get('placeholder', ''),
                        'href': selected_element.get('href', ''),
                        'role': selected_element.get('role', ''),
                        'aria-label': selected_element.get('ariaLabel', ''),
                        'text': selected_element.get('text', '')
                    },
                    'interactive': True,
                    'visible': True,
                    'manually_selected': True,
                    'score': 100  # Give manually selected elements the highest score
                }

                # Store the element in state manager
                if not hasattr(state, 'detected_elements'):
                    state.detected_elements = []

                # Add the manually selected element to the beginning of the list
                state.detected_elements.insert(0, element)

                # Apply QA-specific filtering (though we'll keep the manually selected element)
                qa_elements = [element]  # Start with the manually selected element

                # Add any other elements that match the filtering criteria
                if hasattr(state, 'detected_elements') and len(state.detected_elements) > 1:
                    other_qa_elements = filter_qa_relevant_elements(
                        state.detected_elements[1:],  # Skip the first element which is our manually selected one
                        locator_strategy=locator_strategy,
                        locator_value=locator_value
                    )
                    qa_elements.extend(other_qa_elements)

                state.qa_relevant_elements = qa_elements

                # Show success message with element information
                st.success(f"✓ Element selected: {element['name']} ({element['selector']})")

                # Show the selected element details without using an expander (to avoid nesting)
                st.markdown("**Selected Element Details:**")
                st.json(element)

                # Automatically create element matches for the manually selected element
                # This allows skipping the element matching step (4c)
                _create_element_match_for_manual_selection(state, element, selected_step_table_entry, selected_original_step)
            else:
                st.warning("No element was selected or the selection timed out.")
        except Exception as e:
            st.error(f"Error during interactive element selection: {e}")
            import traceback
            st.error(traceback.format_exc())


def _create_element_match_for_manual_selection(state, element, selected_step_table_entry, selected_original_step):
    """Create element match for manually selected element for the selected test case step."""
    try:
        # Prepare the test case and step for automatic matching
        test_case_id = state.selected_test_case.get('Test Case ID')
        step_no = str(selected_original_step.get('Step No'))

        # Create a match entry for the manually selected element
        element_match = {
            'element': element,
            'action': selected_step_table_entry.get('action', 'interact with'),
            'score': 1.0,  # Perfect match score
            'reasoning': 'This element was manually selected by the user.',
            'manually_selected': True
        }

        # Create the element matches structure
        if not hasattr(state, 'element_matches'):
            state.element_matches = {}

        if test_case_id not in state.element_matches:
            state.element_matches[test_case_id] = {}

        state.element_matches[test_case_id][step_no] = [element_match]
        state.step_matches = state.element_matches

        # Set the LLM step analysis
        # Analyze if the step requires test data
        test_data_analysis = analyze_step_for_test_data(
            selected_step_table_entry,
            selected_original_step.get('Test Steps')
        )

        # Store the complete analysis in session state
        state.llm_step_analysis = {
            "requires_ui_element": True,
            "reason": "Manually selected UI element will be used for this step.",
            "matches": [element_match],
            "requires_test_data": test_data_analysis["requires_test_data"],
            "test_data_reason": test_data_analysis["reason"],
            "data_types": test_data_analysis["data_types"]
        }

        # Store test data analysis separately for easier access
        state.test_data_analysis = test_data_analysis

        # Automatically set test_data_skipped flag for navigation steps
        if not test_data_analysis["requires_test_data"]:
            state.test_data_skipped = True
            state.test_data = getattr(state, 'test_data', {})  # Ensure test_data exists
            st.success("✓ Test data configuration automatically skipped for this navigation test case step.")
            st.info("You can proceed directly to Application Stage 6 to generate the test script.")

        # Inform the user that element matching is complete
        if not test_data_analysis["requires_test_data"]:
            st.info("✓ Element matching automatically completed with your manually selected element. You can proceed directly to Application Stage 6.")

            # Automatically advance to Stage 6 since no test data is needed
            if state.current_stage == StateStage.STAGE4_DETECT:
                state.advance_to(StateStage.STAGE6_GENERATE, "Element matching completed, no test data needed - advancing to Stage 6")

                # Force state update in session state
                st.session_state['state'] = state
                st.session_state['stage_progression_message'] = "✅ Element matching completed. Proceeding to Stage 6 (no test data needed)."

                # Call st.rerun() to immediately refresh the UI
                st.rerun()
                return
        else:
            st.info("✓ Element matching automatically completed with your manually selected element. You can proceed to Application Stage 5.")

            # Automatically advance to Stage 5 for test data configuration
            if state.current_stage == StateStage.STAGE4_DETECT:
                state.advance_to(StateStage.STAGE5_DATA, "Element matching completed - advancing to Stage 5 for test data")

                # Force state update in session state
                st.session_state['state'] = state
                st.session_state['stage_progression_message'] = "✅ Element matching completed. Proceeding to Stage 5 for test data configuration."

                # Call st.rerun() to immediately refresh the UI
                st.rerun()
                return
    except Exception as e:
        st.warning(f"Could not automatically complete element matching: {e}. Please use the 'Match Elements with Step' button in Application Stage 4c.")


def _handle_automatic_element_detection(state, selected_step_table_entry):
    """Handle automatic element detection for the selected test case step."""
    with st.spinner("Detecting and filtering UI elements from the website..."):
        try:
            # Get locator strategy and value from the step table entry
            locator_strategy = None
            locator_value = None

            if selected_step_table_entry:
                locator_strategy = selected_step_table_entry.get('locator_strategy')
                locator_value = selected_step_table_entry.get('locator')

                # Display the locator strategy being used
                if locator_strategy and locator_strategy.lower() not in ['none', 'n/a', '']:
                    st.info(f"Using locator strategy: {locator_strategy}" +
                           (f" with value: {locator_value}" if locator_value else ""))

            # For step 1, extract from URL; for step 2+, extract from browser
            website_url = state.website_url if hasattr(state, 'website_url') and state.website_url else "https://example.com"
            logger.info(f"Using website URL for element detection: {website_url}")

            if hasattr(state, 'test_browser') and state.test_browser:
                # Use the live browser instance for extraction
                browser = state.test_browser
                elements = detect_elements(browser)  # This function should handle browser instance
            else:
                # First time: extract from URL with locator strategy
                elements = detect_elements_advanced(
                    website_url,
                    locator_strategy=locator_strategy,
                    locator_value=locator_value
                )
            state.detected_elements = elements

            # Apply QA-specific filtering to the detected elements using locator strategy
            qa_elements = filter_qa_relevant_elements(
                elements,
                locator_strategy=locator_strategy,
                locator_value=locator_value
            )
            state.qa_relevant_elements = qa_elements

            # Ensure step_elements is set for test data generation
            state.step_elements = qa_elements if qa_elements else elements

            # Show success message with locator strategy information
            if locator_strategy and locator_strategy.lower() not in ['none', 'n/a', '']:
                st.success(f"✓ Detected {len(elements)} UI elements and filtered to {len(qa_elements)} QA-relevant elements using {locator_strategy} strategy.")
            else:
                st.success(f"✓ Detected {len(elements)} UI elements and filtered to {len(qa_elements)} QA-relevant elements.")

            # Show filtered QA-relevant UI elements
            st.markdown("**QA-Relevant UI Elements:**")
            if qa_elements:
                st.write(f"{len(qa_elements)} QA-relevant UI elements detected:")
                st.json(qa_elements)
            else:
                st.info("No QA-relevant UI elements detected.")
        except Exception as e:
            st.error(f"Error detecting elements: {e}")


def _handle_element_matching(state, selected_step_table_entry, selected_original_step):
    """Handle element matching for the selected test case step."""
    with st.expander("Element Matching", expanded=True):
        # Check if automatic stage progression has already been handled
        test_case_id = state.selected_test_case.get('Test Case ID')
        step_no = str(selected_original_step.get('Step No'))

        # Check if we already have element matches for this step
        has_existing_matches = (
            hasattr(state, 'element_matches') and
            state.element_matches and
            test_case_id in state.element_matches and
            step_no in state.element_matches[test_case_id]
        )

        # Check if we already have a manually selected element with matches
        has_manual_selection = False
        if has_existing_matches:
            has_manual_selection = any(match.get('manually_selected', False)
                                     for match in state.element_matches[test_case_id][step_no])

        if has_manual_selection:
            st.success("✓ Element manually selected")

            # Show the manually selected element
            if hasattr(state, 'detected_elements') and state.detected_elements:
                for element in state.detected_elements:
                    if element.get('manually_selected', False):
                        st.markdown("**Selected Element Details:**")
                        st.json(element)
                        break

        # Check if this is a navigation step that doesn't require UI elements
        elif not hasattr(state, 'step_table_analysis') or not state.step_table_analysis.get("requires_ui_elements", True) or selected_step_table_entry.get('locator_strategy') in ["", "none", "n/a", "url"]:
            # Check if automatic progression has already been handled
            if has_existing_matches:
                st.success("✓ Navigation step - already processed")

                # Show guidance based on test data requirements
                if hasattr(state, 'test_data_analysis') and state.test_data_analysis:
                    if not state.test_data_analysis["requires_test_data"]:
                        st.info("✓ Ready for Stage 6 (no test data needed)")
                    else:
                        st.info("✓ Ready for Stage 5 (test data configuration)")
            else:
                st.success("✓ Navigation step - no elements needed")
                st.info("✓ Step processed automatically - check stage progression")

        # Check if element detection is required but not done
        elif hasattr(state, 'step_table_analysis') and state.step_table_analysis.get("requires_ui_elements", True) and selected_step_table_entry.get('locator_strategy') not in ["", "none", "n/a", "url"] and not hasattr(state, 'detected_elements'):
            st.warning("⚠️ Please detect UI elements first")
        else:
            # Prepare for element matching
            if hasattr(state, 'qa_relevant_elements') and state.qa_relevant_elements:
                elements_for_matching = state.qa_relevant_elements
                element_count = len(elements_for_matching)
                st.info(f"Found {element_count} QA-relevant elements")
            elif hasattr(state, 'detected_elements') and state.detected_elements:
                elements_for_matching = state.detected_elements
                element_count = len(elements_for_matching)
                st.info(f"Found {element_count} elements")
            else:
                st.warning("⚠️ No elements detected")
                return

            state.step_elements = elements_for_matching

            # Button to analyze the selected step with detected elements
            # Disable the button if UI elements are required but not detected or if we already have a manual selection
            button_disabled = (hasattr(state, 'step_table_analysis') and
                              state.step_table_analysis.get("requires_ui_elements", True) and
                              selected_step_table_entry.get('locator_strategy') not in ["", "none", "n/a", "url"] and
                              not hasattr(state, 'detected_elements')) or has_manual_selection

            # Use a single column for the match button to make it more prominent
            match_button = st.button(
                "🔄 Match Elements with Step",
                disabled=button_disabled,
                key="match_elements_btn",
                help="Match UI elements with the selected step" if not has_manual_selection else "Not needed - element already selected",
                use_container_width=True
            )

            if match_button:
                _perform_element_matching(state, selected_step_table_entry, selected_original_step, elements_for_matching)


def _perform_element_matching(state, selected_step_table_entry, selected_original_step, elements_for_matching):
    """Perform element matching using AI for the selected test case step."""
    if hasattr(state, 'step_table_analysis') and state.step_table_analysis.get("requires_ui_elements", True) and selected_step_table_entry.get('locator_strategy') not in ["", "none", "n/a", "url"] and not hasattr(state, 'detected_elements'):
        st.error("Please detect UI elements first using the 'Detect UI Elements' button above")
    else:
        with st.spinner("Analyzing test case step and matching elements..."):
            try:
                # Prepare the test case and step for analysis
                # Use both the original step and the step table entry
                test_case_for_analysis = {
                    "id": state.selected_test_case.get('Test Case ID'),
                    "objective": state.selected_test_case.get('Test Case Objective'),
                    "steps": [{
                        "step_no": str(selected_original_step.get('Step No')),
                        "action": selected_original_step.get('Test Steps'),
                        "expected": selected_original_step.get('Expected Result'),
                        # Add step table information
                        "step_type": selected_step_table_entry.get('step_type'),
                        "locator_strategy": selected_step_table_entry.get('locator_strategy'),
                        "locator": selected_step_table_entry.get('locator'),
                        "test_data_param": selected_step_table_entry.get('test_data_param'),
                        "assertion_type": selected_step_table_entry.get('assertion_type')
                    }]
                }

                # Match elements with the selected step using context-aware analysis
                use_ai_matching = True  # This should be replaced with the actual value from state

                # Check if match_elements_with_ai function is available
                if 'match_elements_with_ai' in globals() and callable(match_elements_with_ai) and use_ai_matching:
                    logger.info("Using AI-powered element matching")
                    element_matches = match_elements_with_ai(
                        test_case_for_analysis,
                        elements_for_matching,
                        state.google_api_key
                    )
                else:
                    st.error("AI element matching function is not available. Please check the core.element_matching module.")
                    element_matches = {}

                if not element_matches or not isinstance(element_matches, dict):
                    st.warning("No valid element matches returned from analysis.")
                    state.step_matches = {}
                    state.element_matches = {}
                    state.llm_step_analysis = {}
                else:
                    # Store matches in state manager
                    state.step_matches = element_matches
                    state.element_matches = element_matches
                    st.success("✓ Element matching completed")

                    # Extract LLM step analysis info from the result
                    tc_id = test_case_for_analysis["id"]
                    step_no = test_case_for_analysis["steps"][0]["step_no"]
                    step_result = element_matches.get(tc_id, {}).get(str(step_no), [])
                    requires_ui_element = bool(step_result)

                    # Analyze if the step requires test data
                    test_data_analysis = analyze_step_for_test_data(
                        selected_step_table_entry,
                        selected_original_step.get('Test Steps')
                    )

                    # Store the complete analysis in state manager
                    state.llm_step_analysis = {
                        "requires_ui_element": requires_ui_element,
                        "reason": "Matched UI element(s) found." if requires_ui_element else "No relevant UI element required for this step.",
                        "matches": step_result,
                        "requires_test_data": test_data_analysis["requires_test_data"],
                        "test_data_reason": test_data_analysis["reason"],
                        "data_types": test_data_analysis["data_types"]
                    }

                    # Store test data analysis separately for easier access
                    state.test_data_analysis = test_data_analysis

                    # Automatically set test_data_skipped flag for navigation steps
                    if not test_data_analysis["requires_test_data"]:
                        state.test_data_skipped = True
                        state.test_data = getattr(state, 'test_data', {})  # Ensure test_data exists
                        state.step_ready_for_script = True  # Mark the step as ready for script generation
                        st.success("✓ Test data configuration automatically skipped for this navigation test case step.")
                        st.info("You can proceed directly to Application Stage 6 to generate the test script.")

                        # Automatically advance to Stage 6 since no test data is needed
                        if state.current_stage == StateStage.STAGE4_DETECT:
                            state.advance_to(StateStage.STAGE6_GENERATE, "AI element matching completed, no test data needed - advancing to Stage 6")

                            # Force state update in session state
                            st.session_state['state'] = state
                            st.session_state['stage_progression_message'] = "✅ Element matching completed. Proceeding to Stage 6 (no test data needed)."

                            # Call st.rerun() to immediately refresh the UI
                            st.rerun()
                            return
                    else:
                        # Automatically advance to Stage 5 for test data configuration
                        if state.current_stage == StateStage.STAGE4_DETECT:
                            state.advance_to(StateStage.STAGE5_DATA, "AI element matching completed - advancing to Stage 5 for test data")

                            # Force state update in session state
                            st.session_state['state'] = state
                            st.session_state['stage_progression_message'] = "✅ Element matching completed. Proceeding to Stage 5 for test data configuration."

                            # Call st.rerun() to immediately refresh the UI
                            st.rerun()
                            return

                    # Display matches
                    _display_element_matches(state, tc_id, step_no, step_result, selected_step_table_entry)
            except Exception as e:
                st.error(f"Error during LLM analysis or element matching: {e}")


def _display_previous_step_context(state, selected_step):
    """Display context from previous steps to maintain continuity in the workflow."""
    # Check if we have any completed steps and step context
    if (hasattr(state, 'completed_steps') and state.completed_steps and
        hasattr(state, 'step_context') and state.step_context):

        # Get the current step number
        current_step_no = str(selected_step.get('Step No'))

        # Find previous steps that have been completed
        previous_steps = [step for step in state.completed_steps if step != current_step_no]

        if previous_steps:
            with st.expander("Previous Steps Context", expanded=False):
                # Create tabs for each previous step
                if len(previous_steps) > 1:
                    tabs = st.tabs([f"Step {step}" for step in previous_steps])

                    for i, step_no in enumerate(previous_steps):
                        with tabs[i]:
                            _display_step_context_content(state, step_no)
                else:
                    # Just one previous step
                    _display_step_context_content(state, previous_steps[0])


def _display_step_context_content(state, step_no):
    """Helper function to display the content of a step context."""
    if step_no in state.step_context:
        step_ctx = state.step_context[step_no]

        # Show elements if available
        if step_ctx.get("elements"):
            st.markdown(f"**UI Elements:** {len(step_ctx['elements'])} elements")

        # Show test data if available
        if step_ctx.get("test_data"):
            st.markdown("**Test Data:**")
            for key, value in step_ctx["test_data"].items():
                st.markdown(f"- {key}: `{value}`")

        # Show script path if available
        if step_ctx.get("script_path"):
            st.markdown(f"**Script:** {os.path.basename(step_ctx['script_path'])}")
    else:
        st.info(f"No context for Step {step_no}")


def _display_element_matches(state, tc_id, step_no, step_result, selected_step_table_entry):
    """
    Display element matches in an organized and user-friendly format.
    """
    st.markdown("**Element Matches:**")

    # Get locator strategy from step table entry
    locator_strategy = None
    locator_value = None
    if selected_step_table_entry:
        locator_strategy = selected_step_table_entry.get('locator_strategy')
        locator_value = selected_step_table_entry.get('locator')

    if step_result:
        for i, match in enumerate(step_result):
            element = match.get('element', {})
            element_name = element.get('name', 'Unknown')
            selector = element.get('selector', 'Unknown')
            action = match.get('action', 'Unknown')

            # Check if element matches the locator strategy
            locator_match = False
            if locator_strategy and locator_strategy.lower() not in ['none', 'n/a', '']:
                attrs = element.get('attributes', {})
                strategy_to_attribute = {
                    'id': 'id', 'name': 'name', 'css': 'selector', 'xpath': 'xpath',
                    'tag': 'tag', 'class': 'class', 'link_text': 'text',
                    'partial_link_text': 'text', 'aria': 'role'
                }

                attribute = strategy_to_attribute.get(locator_strategy.lower())
                if attribute:
                    if attribute in ['selector', 'xpath']:
                        locator_match = True
                    else:
                        attr_value = attrs.get(attribute, '')
                        if attr_value:
                            locator_match = True
                            if locator_value:
                                if locator_strategy.lower() == 'partial_link_text':
                                    locator_match = locator_value.lower() in attr_value.lower()
                                else:
                                    locator_match = attr_value.lower() == locator_value.lower()

            # Display element with match indicator
            match_indicator = f"✅ *{locator_strategy}*" if locator_match else ""
            st.markdown(f"**Match {i+1}:** {element_name} {match_indicator}")
            st.markdown(f"- Action: {action}")

            # Show confidence score if available
            if 'score' in match:
                score = match.get('score', 0)
                st.progress(score)
                st.markdown(f"- Confidence: {score:.2f}")

            # Show reasoning if available
            if 'reasoning' in match:
                st.markdown("**Reasoning:**")
                st.markdown(match.get('reasoning'))
    else:
        st.info("No UI element required for this step.")

    # Show warning if no elements match the locator strategy
    if locator_strategy and locator_strategy.lower() not in ['none', 'n/a', ''] and step_result:
        matches_locator = any(
            match.get('element', {}).get('locator_strategy_match', False)
            for match in step_result
        )
        if not matches_locator:
            st.warning(f"⚠️ No elements match locator: {locator_strategy}")
